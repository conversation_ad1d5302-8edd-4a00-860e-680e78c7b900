export interface User {
  id: string
  name: string
  email: string
  userType: 'manager' | 'staff' | 'client'
  categories?: string[] // For managers - array of category IDs
  managerId?: string // For staff - ID of assigned manager
  createdAt?: string
  updatedAt?: string
}

export interface CreateUserFormData {
  name: string
  email: string
  password: string
  userType: 'manager' | 'staff' | 'client'
  categories?: string[] // For managers
  managerId?: string // For staff
}
