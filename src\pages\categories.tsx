import { useEffect, useState } from 'react'
import { Plus, Edit, Trash2, Eye, FolderOpen, Image } from 'lucide-react'
import { toast } from 'sonner'
import type { Category } from '../types/category'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { fetchCategories, deleteCategory } from '../store/slices/categoriesSlice'
import ConfirmationModal from '../components/ConfirmationModal'
import AddCategoryModal from '../components/AddCategoryModal'
import EditCategoryModal from '../components/EditCategoryModal'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../components/ui/dialog'
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from '../components/ui/drawer'
import { useIsMobile } from '../hooks/use-mobile'

const Categories = () => {
  const dispatch = useAppDispatch()
  const { categories, isLoading, isDeleting, error } = useAppSelector((state: any) => state.categories)
  const isMobile = useIsMobile()
  
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [categoryToDelete, setCategoryToDelete] = useState<string | null>(null)
  const [detailsModalOpen, setDetailsModalOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [addModalOpen, setAddModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)

  useEffect(() => {
    dispatch(fetchCategories())
  }, [dispatch])

  console.log("categories",categories)

  useEffect(() => {
    if (error) {
      toast.error(error)
    }
  }, [error])

  // Get only parent categories (categories without parentCategory or parentCategoryId)
  const parentCategories = categories.filter((cat: any) => !cat.parentCategory && !cat.parentCategoryId)

  const handleDeleteClick = (categoryId: string) => {
    setCategoryToDelete(categoryId)
    setDeleteModalOpen(true)
  }

  const handleEditClick = (category: Category) => {
    setSelectedCategory(category)
    setEditModalOpen(true)
  }

  const handleViewDetails = (category: Category) => {
    setSelectedCategory(category)
    setDetailsModalOpen(true)
  }

  const confirmDelete = async () => {
    if (categoryToDelete) {
      try {
        await dispatch(deleteCategory(categoryToDelete)).unwrap()
        toast.success('Category deleted successfully')
        setDeleteModalOpen(false)
        setCategoryToDelete(null)
      } catch (error) {
        toast.error('Failed to delete category')
      }
    }
  }

  const handleCloseDeleteModal = () => {
    setDeleteModalOpen(false)
    setCategoryToDelete(null)
  }

  // Get subcategories for a parent category
  const getSubcategories = (parentId: string) => {
    return categories.filter((cat: any) =>
      cat.parentCategory === parentId || cat.parentCategoryId === parentId
    )
  }

  const getSubcategoriesCount = (categoryId: string) => {
    return getSubcategories(categoryId).length
  }

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4 sm:p-6">
      {/* Header */}
      <div className="mb-6 sm:mb-8 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">Categories</h1>
          <p className="text-sm sm:text-base text-gray-600">
            Manage your product categories and subcategories.
          </p>
        </div>
        <button
          onClick={() => setAddModalOpen(true)}
          className="bg-[#BE935E] text-white px-3 sm:px-4 py-2 rounded-lg transition-colors flex items-center gap-2 text-sm sm:text-base w-full sm:w-auto justify-center"
        >
          <Plus className="w-4 h-4 sm:w-5 sm:h-5" />
          Add Category
        </button>
      </div>

      {/* Categories Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        {parentCategories.length === 0 ? (
          <div className="text-center py-16">
            <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center">
              <FolderOpen className="w-8 h-8 text-blue-500" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              No categories found
            </h3>
            <p className="text-gray-600 max-w-md mx-auto">
              There are no categories in the system yet. Start by adding your first category to get started.
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full divide-y divide-gray-200" style={{ minWidth: '600px' }}>
              <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
                <tr>
                  <th className="px-2 sm:px-4 lg:px-6 py-3 sm:py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider" style={{ minWidth: '50px' }}>
                    #
                  </th>
                  <th className="px-2 sm:px-4 lg:px-6 py-3 sm:py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider" style={{ minWidth: '60px' }}>
                    Icon
                  </th>
                  <th className="px-2 sm:px-4 lg:px-6 py-3 sm:py-4 text-xs font-bold text-gray-700 uppercase tracking-wider text-center" style={{ minWidth: '150px' }}>
                    Title
                  </th>
                  <th className="px-2 sm:px-4 lg:px-6 py-3 sm:py-4 text-center text-xs font-bold text-gray-700 uppercase tracking-wider" style={{ minWidth: '120px' }}>
                    Subcategories
                  </th>
                  <th className="px-2 sm:px-4 lg:px-6 py-3 sm:py-4 text-center text-xs font-bold text-gray-700 uppercase tracking-wider" style={{ minWidth: '150px' }}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-100">
                {parentCategories.map((category: Category, index: number) => (
                  <tr key={category.id} className="hover:bg-blue-50 transition-colors duration-150">
                    <td className="px-2 sm:px-4 lg:px-6 py-3 sm:py-4 lg:py-5 whitespace-nowrap">
                      <div className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-xs sm:text-sm font-bold text-blue-600">
                          {index + 1}
                        </span>
                      </div>
                    </td>
                    <td className="px-2 sm:px-4 lg:px-6 py-3 sm:py-4 lg:py-5 whitespace-nowrap">
                      <div className="flex-shrink-0">
                        {(category.icon?.url || (category as any).icon?.url) ? (
                          <img
                            src={category.icon?.url || (category as any).icon?.url}
                            alt={category.title}
                            className="w-4 h-4 sm:w-8 sm:h-8 lg:w-7 lg:h-7 rounded-lg object-cover "
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              target.nextElementSibling?.classList.remove('hidden');
                            }}
                          />
                        ) : null}
                        <div className={`w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-gray-200 rounded-lg flex items-center justify-center shadow-md ${(category.icon?.url || (category as any).icon?.url) ? 'hidden' : ''}`}>
                          <Image className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-gray-400" />
                        </div>
                      </div>
                    </td>
                    <td className="px-2 sm:px-4 lg:px-6 py-3 sm:py-4 lg:py-5">
                      <div className="min-w-0">
                        <div className="text-xs sm:text-sm lg:text-base font-semibold text-gray-900 truncate">
                          {category.title}
                        </div>
                        
                      </div>
                    </td>
                    <td className="px-2 sm:px-4 lg:px-6 py-3 sm:py-4 lg:py-5 whitespace-nowrap text-center">
                      <span className="inline-flex px-2 sm:px-3 py-1 text-xs font-bold rounded-full bg-blue-100 text-blue-800">
                        {/* <span className="hidden sm:inline">{getSubcategoriesCount(category.id)} subcategories</span> */}
                        <span className="">{getSubcategoriesCount(category.id)}</span>
                      </span>
                    </td>
                    <td className="px-2 sm:px-4 lg:px-6 py-3 sm:py-4 lg:py-5 whitespace-nowrap text-center">
                      <div className="flex items-center justify-center space-x-1 sm:space-x-2">
                        <button
                          onClick={() => handleViewDetails(category)}
                          className="bg-green-100 text-green-600 hover:bg-green-200 hover:text-green-700 p-1 sm:p-1.5 lg:p-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                          title="View Details"
                        >
                          <Eye className="w-3 h-3 sm:w-4 sm:h-4" />
                        </button>
                        <button
                          onClick={() => handleEditClick(category)}
                          className="bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-700 p-1 sm:p-1.5 lg:p-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                          title="Edit"
                        >
                          <Edit className="w-3 h-3 sm:w-4 sm:h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteClick(category.id)}
                          className="bg-red-100 text-red-600 hover:bg-red-200 hover:text-red-700 p-1 sm:p-1.5 lg:p-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                          title="Delete"
                        >
                          <Trash2 className="w-3 h-3 sm:w-4 sm:h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteModalOpen}
        onClose={handleCloseDeleteModal}
        onConfirm={confirmDelete}
        title="Delete Category"
        subtitle="Are you sure you want to delete this category? This action will also delete all subcategories and cannot be undone."
        confirmLabel="Delete"
        confirmColor="danger"
        isLoading={isDeleting}
      />

      {/* Category Details Modal - Responsive */}
      {isMobile ? (
        <Drawer
          open={detailsModalOpen}
          onOpenChange={(open) => {
            setDetailsModalOpen(open)
            if (!open) {
              setSelectedCategory(null)
            }
          }}
        >
          <DrawerContent className="max-h-[85vh]">
            <DrawerHeader className="text-left">
              <DrawerTitle className="flex items-center gap-3 text-lg font-semibold">
                {selectedCategory && (selectedCategory.icon?.url || (selectedCategory as any).icon?.url) && (
                  <img
                    src={selectedCategory.icon?.url || (selectedCategory as any).icon?.url}
                    alt={selectedCategory?.title}
                    className="w-8 h-8 rounded-lg object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                    }}
                  />
                )}
                {selectedCategory?.title}
              </DrawerTitle>
            </DrawerHeader>

            <div className="px-4 pb-4 overflow-y-auto">
              {selectedCategory && (() => {
                const subcategories = getSubcategories(selectedCategory.id);
                return (
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-base font-semibold text-foreground mb-3">
                        Subcategories ({subcategories.length})
                      </h3>

                      {subcategories.length > 0 ? (
                        <div className="space-y-2">
                          {subcategories.map((subcategory: any) => (
                            <div
                              key={subcategory.id}
                              className="flex items-start gap-3 p-3 bg-muted/50 rounded-lg border border-border min-h-[3.5rem]"
                            >
                              {(subcategory.icon?.url || subcategory.icon?.url) ? (
                                <img
                                  src={subcategory.icon?.url || subcategory.icon?.url}
                                  alt={subcategory.title}
                                  className="w-8 h-8 rounded-lg object-cover flex-shrink-0"
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.style.display = 'none';
                                    target.nextElementSibling?.classList.remove('hidden');
                                  }}
                                />
                              ) : null}
                              <div className={`w-8 h-8 bg-muted rounded-lg flex items-center justify-center flex-shrink-0 ${(subcategory.icon?.url || subcategory.icon?.url) ? 'hidden' : ''}`}>
                                <Image className="w-4 h-4 text-muted-foreground" />
                              </div>
                              <div className="flex min-w-0">
                                <span className=" flex text-sm font-medium text-foreground group-hover:text-foreground/90 leading-relaxed break-words">
                                  {subcategory.title}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8">
                          <FolderOpen className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
                          <p className="text-muted-foreground text-sm">
                            No subcategories found
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })()}
            </div>
          </DrawerContent>
        </Drawer>
      ) : (
        <Dialog
          open={detailsModalOpen}
          onOpenChange={(open) => {
            setDetailsModalOpen(open)
            if (!open) {
              setSelectedCategory(null)
            }
          }}
        >
          <DialogContent className="max-w-3xl max-h-[85vh] overflow-hidden flex flex-col">
            <DialogHeader className="flex-shrink-0">
              <DialogTitle className="flex items-center gap-3 text-xl font-semibold">
                {selectedCategory && (selectedCategory.icon?.url || (selectedCategory as any).icon?.url) && (
                  <img
                    src={selectedCategory.icon?.url || (selectedCategory as any).icon?.url}
                    alt={selectedCategory?.title}
                    className="w-8 h-8 rounded-lg object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                    }}
                  />
                )}
                {selectedCategory?.title}
              </DialogTitle>
            </DialogHeader>

            <div className="flex-1 overflow-y-auto space-y-6">
              {selectedCategory && (() => {
                const subcategories = getSubcategories(selectedCategory.id);
                return (
                  <div>
                    <h3 className="text-lg font-semibold text-foreground mb-4">
                      Subcategories ({subcategories.length})
                    </h3>

                    {subcategories.length > 0 ? (
                      <>
                        {/* Card layout for small and medium devices */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 lg:hidden">
                          {subcategories.map((subcategory: any) => (
                            <div
                              key={subcategory.id}
                              className="flex items-start gap-3 p-4 bg-muted/50 rounded-lg border border-border hover:bg-muted/70 transition-colors group min-h-[4rem]"
                            >
                              {(subcategory.icon?.url || subcategory.icon?.url) ? (
                                <img
                                  src={subcategory.icon?.url || subcategory.icon?.url}
                                  alt={subcategory.title}
                                  className="w-8 h-8 rounded-lg object-cover flex-shrink-0"
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.style.display = 'none';
                                    target.nextElementSibling?.classList.remove('hidden');
                                  }}
                                />
                              ) : null}
                              <div className={`w-8 h-8 bg-muted rounded-lg flex items-center justify-center flex-shrink-0 ${(subcategory.icon?.url || subcategory.icon?.url) ? 'hidden' : ''}`}>
                                <Image className="w-4 h-4 text-muted-foreground" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <span className="text-sm font-medium text-foreground group-hover:text-foreground/90 leading-relaxed break-words">
                                  {subcategory.title}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>

                        {/* Chip layout for large devices */}
                        <div className="hidden lg:flex flex-wrap gap-3">
                          {subcategories.map((subcategory: any) => (
                            <div
                              key={subcategory.id}
                              className="inline-flex items-center gap-2 px-4 py-2.5 bg-[#BE935E]/10 text-[#BE935E] border border-[#BE935E]/20 rounded-full text-sm font-medium hover:bg-[#BE935E]/20 transition-colors cursor-default"
                            >
                              {(subcategory.icon?.url || subcategory.icon?.url) ? (
                                <img
                                  src={subcategory.icon?.url || subcategory.icon?.url}
                                  alt={subcategory.title}
                                  className="w-5 h-5 rounded-full object-cover flex-shrink-0"
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.style.display = 'none';
                                    target.nextElementSibling?.classList.remove('hidden');
                                  }}
                                />
                              ) : (
                                <div className="w-5 h-5 bg-[#BE935E]/20 rounded-full flex items-center justify-center flex-shrink-0">
                                  <Image className="w-3 h-3 text-[#BE935E]" />
                                </div>
                              )}
                              <span className="leading-none">
                                {subcategory.title}
                              </span>
                            </div>
                          ))}
                        </div>
                      </>
                    ) : (
                      <div className="text-center py-12">
                        <FolderOpen className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground text-base">
                          No subcategories found
                        </p>
                        <p className="text-muted-foreground text-sm mt-1">
                          Add subcategories to organize your content better
                        </p>
                      </div>
                    )}
                  </div>
                );
              })()}
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Add Category Modal */}
      <AddCategoryModal
        isOpen={addModalOpen}
        onClose={() => setAddModalOpen(false)}
      />

      {/* Edit Category Modal */}
      <EditCategoryModal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        category={selectedCategory}
      />
    </div>
  )
}

export default Categories
