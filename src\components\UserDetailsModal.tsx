import { useState, useEffect } from 'react'
import { X, User, Mail, Calendar, UserCheck, FolderOpen, Users } from 'lucide-react'
import type { User as UserType } from '../types/user'
import type { Category } from '../types/category'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { fetchCategories } from '../store/slices/categoriesSlice'
import { fetchAllUsers } from '../store/slices/usersSlice'
import { formatDate } from '../utils/app.utils'

interface UserDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  user: UserType | null
}

const UserDetailsModal = ({ isOpen, onClose, user }: UserDetailsModalProps) => {
  const dispatch = useAppDispatch()
  const { categories } = useAppSelector((state: any) => state.categories)
  const { users } = useAppSelector((state: any) => state.users)
  const [assignedCategoryDetails, setAssignedCategoryDetails] = useState<Category[]>([])
  const [assignedManager, setAssignedManager] = useState<UserType | null>(null)

  useEffect(() => {
    if (isOpen) {
      if (categories.length === 0) {
        dispatch(fetchCategories())
      }
      if (users.length === 0) {
        dispatch(fetchAllUsers())
      }
    }
  }, [isOpen, categories.length, users.length, dispatch])

  useEffect(() => {
    if (user && user.userType === 'manager' && user.categories && categories.length > 0) {
      const categoryDetails = user.categories
        .map(categoryId => categories.find((cat: Category) => cat.id === categoryId))
        .filter(Boolean) as Category[]
      setAssignedCategoryDetails(categoryDetails)
    } else {
      setAssignedCategoryDetails([])
    }
  }, [user, categories])

  // Update assigned manager when user or users change
  useEffect(() => {
    if (user && user.userType === 'staff' && user.managerId && users.length > 0) {
      const manager = users.find((u: UserType) => u.id === user.managerId)
      setAssignedManager(manager || null)
    } else {
      setAssignedManager(null)
    }
  }, [user, users])

  const getUserTypeColor = (type: string) => {
    switch (type) {
      case 'manager':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'staff':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'client':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const handleClose = () => {
    setAssignedCategoryDetails([])
    onClose()
  }

  if (!isOpen || !user) return null

  return (
    <div className="fixed inset-0 bg-gray-100/50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-2xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-[#BE935E] px-3 sm:px-6 py-3 sm:py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-white rounded-full flex items-center justify-center flex-shrink-0">
              <User className="w-4 h-4 sm:w-6 sm:h-6 text-gray-600" />
            </div>
            <div className="min-w-0 flex-1">
              <h2 className="text-lg sm:text-xl text-left font-bold text-white truncate">User Details</h2>
              <p className="text-blue-100 text-xs sm:text-sm hidden sm:block">View user information and assignments</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-100 cursor-pointer hover:bg-opacity-20 p-1 sm:p-2 rounded-lg transition-colors flex-shrink-0"
          >
            <X className="w-5 h-5 sm:w-6 sm:h-6" />
          </button>
        </div>

        {/* Body */}
        <div className="p-3 sm:p-6 overflow-y-auto max-h-[calc(95vh-120px)] sm:max-h-[calc(90vh-120px)]">
          {/* User Avatar and Basic Info */}
          <div className="flex items-center space-x-3 sm:space-x-4 mb-4 sm:mb-6 p-3 sm:p-4 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 sm:w-16 sm:h-16 bg-[#BE935E] rounded-full flex items-center justify-center shadow-lg flex-shrink-0">
              <span className="text-lg sm:text-xl font-bold text-white text-left">
                {user.name?.charAt(0)?.toUpperCase() || 'U'}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-lg sm:text-xl font-bold text-gray-900 text-left truncate">{user.name}</h3>
              <div className="flex items-center space-x-2 mt-1">
                <span className={`flex px-2 sm:px-3 py-1 text-xs sm:text-sm font-semibold rounded-full border ${getUserTypeColor(user.userType)}`}>
                  {user.userType.charAt(0).toUpperCase() + user.userType.slice(1)}
                </span>
              </div>
            </div>
          </div>

          {/* User Information Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-4 sm:mb-6">
            {/* Email */}
            <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4">
              <div className="flex items-center gap-3 ">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Mail className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600" />
                </div>
                <div className="min-w-0 ">
                  <p className="text-xs sm:text-sm font-medium text-gray-500 flex">Email Address</p>
                  <p className="text-xs sm:text-sm font-semibold text-gray-900 truncate ">{user.email}</p>
                </div>
              </div>
            </div>

            {/* User Type */}
            <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4">
              <div className="flex gap-3">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <UserCheck className="w-4 h-4 sm:w-5 sm:h-5 text-green-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-gray-500 flex">User Type</p>
                  <p className="text-xs sm:text-sm font-semibold text-gray-900 flex">
                    {user.userType.charAt(0).toUpperCase() + user.userType.slice(1)}
                  </p>
                </div>
              </div>
            </div>

            {/* Created Date */}
            <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4">
              <div className="flex gap-3">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Calendar className="w-4 h-4 sm:w-5 sm:h-5 text-purple-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-gray-500 flex">Date Created</p>
                  <p className="text-xs sm:text-sm font-semibold text-gray-900 flex">
                    {formatDate(user.createdAt)}
                  </p>
                </div>
              </div>
            </div>

            {/* Updated Date */}
            <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4">
              <div className="flex gap-3">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Calendar className="w-4 h-4 sm:w-5 sm:h-5 text-green-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-gray-500 flex">Last Updated</p>
                  <p className="text-xs sm:text-sm font-semibold text-gray-900 flex">
                    {formatDate(user.updatedAt)}
                  </p>
                </div>
              </div>
            </div>


          </div>

          {/* Assigned Manager (Only for Staff) */}
          {user.userType === 'staff' && (
            <div className="mb-4 sm:mb-6">
              <h4 className="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4 flex items-center">
                <Users className="w-4 h-4 sm:w-5 sm:h-5 mr-2 text-[#BE935E] flex-shrink-0" />
                <span className="truncate">Assigned Manager</span>
              </h4>

              {assignedManager ? (
                <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4">
                  <div className="flex items-center space-x-3 sm:space-x-4">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 bg-[#BE935E] rounded-full flex items-center justify-center shadow-lg flex-shrink-0">
                      <span className="text-sm sm:text-lg font-bold text-white">
                        {assignedManager.name?.charAt(0)?.toUpperCase() || 'M'}
                      </span>
                    </div>
                    <div className="flex flex-col min-w-0 flex-1">
                      <h5 className="text-sm sm:text-base font-semibold text-gray-900 truncate flex">{assignedManager.name}</h5>
                      <p className="text-xs sm:text-sm text-gray-600 flex items-center mt-1">
                        <Mail className="w-3 h-3 sm:w-4 sm:h-4 mr-1 flex-shrink-0" />
                        <span className="truncate">{assignedManager.email}</span>
                      </p>
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 border border-blue-200 mt-2 w-fit">
                        Manager
                      </span>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 sm:p-6 text-center">
                  <Users className="w-8 h-8 sm:w-12 sm:h-12 text-gray-400 mx-auto mb-2 sm:mb-3" />
                  <p className="text-gray-600 text-xs sm:text-sm">
                    {user.managerId ? 'Manager information not available' : 'No manager assigned'}
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Assigned Categories (Only for Managers) */}
          {user.userType === 'manager' && (
            <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4">
              <div className="flex items-center space-x-2 sm:space-x-3 mb-3 sm:mb-4">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <FolderOpen className="w-4 h-4 sm:w-5 sm:h-5 text-orange-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <h4 className="text-base sm:text-lg font-semibold text-gray-900 truncate flex">Assigned Categories</h4>
                  <p className="text-xs sm:text-sm text-gray-500 flex ">Categories this manager is responsible for</p>
                </div>
              </div>

              {assignedCategoryDetails.length > 0 ? (
                <div className="grid grid-cols-1 gap-2 sm:gap-3">
                  {assignedCategoryDetails.map((category) => (
                    <div key={category.id} className="flex items-center space-x-2 sm:space-x-3 p-2 sm:p-3 bg-gray-50 rounded-lg border border-gray-100">
                      {category.icon?.url ? (
                        <img
                          src={category.icon.url}
                          alt={category.title}
                          className="w-5 h-5 sm:w-6 sm:h-6 rounded-lg object-cover flex-shrink-0"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            target.nextElementSibling?.classList.remove('hidden');
                          }}
                        />
                      ) : null}
                      <div className={`w-6 h-6 sm:w-8 sm:h-8 bg-orange-200 rounded-lg flex items-center justify-center flex-shrink-0 ${category.icon?.url ? 'hidden' : ''}`}>
                        <FolderOpen className="w-3 h-3 sm:w-4 sm:h-4 text-orange-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-xs sm:text-sm font-semibold text-gray-900 truncate">{category.title}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : user.categories && user.categories.length > 0 ? (
                <div className="text-center py-3 sm:py-4">
                  <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  <p className="text-xs sm:text-sm text-gray-500">Loading category details...</p>
                </div>
              ) : (
                <div className="text-center py-6 sm:py-8">
                  <FolderOpen className="w-8 h-8 sm:w-12 sm:h-12 text-gray-400 mx-auto mb-2 sm:mb-3" />
                  <p className="text-xs sm:text-sm text-gray-500">No categories assigned to this manager</p>
                </div>
              )}
            </div>
          )}
        </div>

        
      </div>
    </div>
  )
}

export default UserDetailsModal

