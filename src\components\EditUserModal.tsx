import { useState, useEffect, useRef } from 'react'
import { X, User, Mail, UserCheck, Check, Users, ChevronDown, ChevronUp } from 'lucide-react'
import { toast } from 'sonner'
import type { User as UserType } from '../types/user'
import type { Category } from '../types/category'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { updateUser, fetchAllUsers } from '../store/slices/usersSlice'
import { fetchCategories } from '../store/slices/categoriesSlice'

interface EditUserModalProps {
  isOpen: boolean
  onClose: () => void
  user: UserType | null
}

interface FormData {
  name: string
  email: string
  userType: 'manager' | 'staff' | 'client'
  categories?: string[]
  managerId?: string
}

interface FormErrors {
  name?: string
  email?: string
  userType?: string
  categories?: string
  managerId?: string
}

export default function EditUserModal({ isOpen, onClose, user }: EditUserModalProps) {
  const dispatch = useAppDispatch()
  const { updateLoading, users } = useAppSelector((state: any) => state.users)
  const { categories, isLoading: categoriesLoading } = useAppSelector((state: any) => state.categories)

  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    userType: 'staff',
    categories: [],
    managerId: ''
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [isManagerDropdownOpen, setIsManagerDropdownOpen] = useState(false)
  const managerDropdownRef = useRef<HTMLDivElement>(null)

  const isUpdating = updateLoading === user?.id

  // Fetch categories and users when modal opens
  useEffect(() => {
    if (isOpen) {
      dispatch(fetchCategories())
      if (users.length === 0) {
        dispatch(fetchAllUsers())
      }
    }
  }, [dispatch, isOpen, users.length])

  // Set form data when user prop changes
  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        userType: user.userType || 'staff',
        categories: user.categories || [],
        managerId: user.managerId || ''
      })
    }
  }, [user])

  // Handle click outside to close manager dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (managerDropdownRef.current && !managerDropdownRef.current.contains(event.target as Node)) {
        setIsManagerDropdownOpen(false)
      }
    }

    if (isManagerDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isManagerDropdownOpen])

  // Get only parent categories (categories without parentCategory or parentCategoryId)
  const parentCategories = categories.filter((cat: any) => !cat.parentCategory && !cat.parentCategoryId)

  // Get subcategories for a parent category
  const getSubcategories = (parentId: string) => {
    return categories.filter((cat: any) =>
      cat.parentCategory === parentId || cat.parentCategoryId === parentId
    )
  }

  // Get managers for staff assignment (exclude current user if they are a manager)
  const managers = users.filter((u: UserType) => u.userType === 'manager' && u.id !== user?.id)

  // Handle manager selection
  const handleManagerSelect = (managerId: string) => {
    setFormData(prev => ({
      ...prev,
      managerId
    }))
    setIsManagerDropdownOpen(false)

    // Clear error when user selects a manager
    if (errors.managerId) {
      setErrors(prev => ({
        ...prev,
        managerId: undefined
      }))
    }
  }

  // Get selected manager details
  const selectedManager = managers.find((manager: UserType) => manager.id === formData.managerId)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))

    // Clear error when user types
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined,
      }))
    }
  }

  const handleCategoryToggle = (categoryId: string) => {
    setFormData(prev => {
      const currentCategories = prev.categories || []
      const isSelected = currentCategories.includes(categoryId)

      // If we're deselecting a parent category, also deselect all its subcategories
      if (isSelected) {
        const subcategories = getSubcategories(categoryId)
        const subcategoryIds = subcategories.map((sub: any) => sub.id)
        const filteredCategories = currentCategories.filter(id =>
          id !== categoryId && !subcategoryIds.includes(id)
        )

        return {
          ...prev,
          categories: filteredCategories
        }
      } else {
        // If selecting a parent category, just add it
        return {
          ...prev,
          categories: [...currentCategories, categoryId]
        }
      }
    })

    // Clear category error when user makes selection
    if (errors.categories) {
      setErrors(prev => ({
        ...prev,
        categories: undefined,
      }))
    }
  }

  const handleSubcategoryToggle = (subcategoryId: string, parentCategoryId: string) => {
    setFormData(prev => {
      const currentCategories = prev.categories || []
      const isSelected = currentCategories.includes(subcategoryId)
      const isParentSelected = currentCategories.includes(parentCategoryId)

      if (isSelected) {
        // Remove subcategory
        return {
          ...prev,
          categories: currentCategories.filter(id => id !== subcategoryId)
        }
      } else {
        // Add subcategory and ensure parent is also selected
        const newCategories = [...currentCategories]
        if (!isParentSelected) {
          newCategories.push(parentCategoryId)
        }
        newCategories.push(subcategoryId)

        return {
          ...prev,
          categories: newCategories
        }
      }
    })

    // Clear category error when user makes selection
    if (errors.categories) {
      setErrors(prev => ({
        ...prev,
        categories: undefined,
      }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required'
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid'
    }

    if (!formData.userType) {
      newErrors.userType = 'User type is required'
    }

    if (formData.userType === 'manager' && (!formData.categories || formData.categories.length === 0)) {
      newErrors.categories = 'Managers must be assigned to at least one category'
    }

    if (formData.userType === 'staff' && !formData.managerId) {
      newErrors.managerId = 'Staff must be assigned to a manager'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user) return

    if (validateForm()) {
      try {
        await dispatch(updateUser({ 
          userId: user.id, 
          userData: formData 
        })).unwrap()
        
        toast.success('User updated successfully')
        onClose()
      } catch (error) {
        toast.error('Failed to update user')
      }
    }
  }

  const handleClose = () => {
    setFormData({
      name: '',
      email: '',
      userType: 'staff',
      categories: [],
      managerId: ''
    })
    setErrors({})
    setIsManagerDropdownOpen(false)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-gray-900/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Edit User</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={isUpdating}
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Body */}
        <div className="p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Name Field */}
            <div>
              <label htmlFor="name" className="flex text-sm font-medium text-gray-700 mb-1">
                Name
              </label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.name ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter user name"
                  disabled={isUpdating}
                />
              </div>
              {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
            </div>

            {/* Email Field */}
            <div>
              <label htmlFor="email" className="flex text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.email ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter email address"
                  disabled={true} // Email cannot be changed
                />
              </div>
              {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
              <p className="text-gray-500 text-xs mt-1 flex">Email cannot be changed</p>
            </div>

            {/* User Type Field */}
            {/* <div>
              <label htmlFor="userType" className="flex text-sm font-medium text-gray-700 mb-1">
                User Type
              </label>
              <div className="relative">
                <UserCheck className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <select
                  id="userType"
                  name="userType"
                  value={formData.userType}
                  onChange={handleChange}
                  className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.userType ? 'border-red-500' : 'border-gray-300'
                  }`}
                  disabled={isUpdating}
                >
                  <option value="manager">Manager</option>
                  <option value="staff">Staff</option>
                  <option value="client">Client</option>
                </select>
              </div>
              {errors.userType && <p className="text-red-500 text-sm mt-1">{errors.userType}</p>}
            </div> */}

            {/* Manager Assignment for Staff */}
            {formData.userType === 'staff' && (
              <div className="mb-4">
                <label htmlFor="managerId" className="flex text-sm font-medium text-gray-700 mb-1">
                  Assigned Manager *
                </label>
                <div className="relative">
                  <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <select
                    id="managerId"
                    name="managerId"
                    value={formData.managerId}
                    onChange={handleChange}
                    className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.managerId ? 'border-red-500' : 'border-gray-300'
                    }`}
                    disabled={isUpdating}
                  >
                    <option value="">Select a manager</option>
                    {managers.map((manager: UserType) => (
                      <option key={manager.id} value={manager.id}>
                        {manager.name} ({manager.email})
                      </option>
                    ))}
                  </select>
                </div>
                {errors.managerId && <p className="text-red-500 text-sm mt-1">{errors.managerId}</p>}
                {managers.length === 0 && (
                  <p className="text-gray-500 text-sm mt-1">No managers available. Please create a manager first.</p>
                )}
              </div>
            )}

            {/* Category Assignment for Managers */}
            {formData.userType === 'manager' && (
              <div className="mb-6">
                <label className="flex text-sm font-medium text-gray-700 mb-2">
                  Assigned Categories *
                </label>
                {categoriesLoading ? (
                  <div className="flex justify-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#BE935E]"></div>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-60 overflow-y-auto border border-gray-200 rounded-lg p-3">
                    {parentCategories.length === 0 ? (
                      <p className="text-gray-500 text-sm">No categories available</p>
                    ) : (
                      parentCategories.map((category: Category) => {
                        const subcategories = getSubcategories(category.id)
                        const isParentSelected = formData.categories?.includes(category.id) || false

                        return (
                          <div key={category.id} className="space-y-2">
                            {/* Parent Category */}
                            <div className="flex items-center space-x-3">
                              <button
                                type="button"
                                onClick={() => handleCategoryToggle(category.id)}
                                disabled={isUpdating}
                                className={`flex items-center justify-center w-5 h-5 rounded border-2 transition-all duration-200 ${
                                  isParentSelected
                                    ? 'bg-[#BE935E] border-[#BE935E] text-white'
                                    : 'border-gray-300 hover:border-[#BE935E]'
                                }`}
                              >
                                {isParentSelected && <Check className="w-3 h-3" />}
                              </button>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm font-medium text-gray-900">
                                  {category.title}
                                </span>
                              </div>
                            </div>

                            {/* Subcategories - only show if parent is selected */}
                            {isParentSelected && subcategories.length > 0 && (
                              <div className="ml-8 pl-3">
                                <div className="flex flex-wrap gap-2 mt-2">
                                  {subcategories.map((subcategory: Category) => {
                                    const isSelected = formData.categories?.includes(subcategory.id) || false
                                    return (
                                      <button
                                        key={subcategory.id}
                                        type="button"
                                        onClick={() => handleSubcategoryToggle(subcategory.id, category.id)}
                                        disabled={isUpdating}
                                        className={`px-3 py-1.5 text-xs font-medium rounded-full border transition-all duration-200 ${
                                          isSelected
                                            ? 'bg-[#BE935E] text-white border-[#BE935E] shadow-sm'
                                            : 'bg-white text-gray-600 border-gray-300 hover:border-[#BE935E] hover:text-[#BE935E] hover:bg-[#BE935E]/5'
                                        }`}
                                      >
                                        {subcategory.title}
                                      </button>
                                    )
                                  })}
                                </div>
                              </div>
                            )}
                          </div>
                        )
                      })
                    )}
                  </div>
                )}
                {errors.categories && <p className="text-red-500 text-sm mt-1">{errors.categories}</p>}
              </div>
            )}
          </form>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t border-gray-200">
          <button
            type="button"
            onClick={handleClose}
            className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            disabled={isUpdating}
          >
            Cancel
          </button>
          <button
            type="submit"
            onClick={handleSubmit}
            disabled={isUpdating}
            className="px-4 py-2 bg-[#BE935E]  text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isUpdating && (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            )}
            {isUpdating ? 'Updating...' : 'Update User'}
          </button>
        </div>
      </div>
    </div>
  )
}
